<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blue Wire Capital Extension Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #0a66c2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #004182;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Blue Wire Capital Extension Test</h1>
    
    <div class="test-section">
        <h2>Instructions</h2>
        <p>1. Load this page in Chrome with the Blue Wire Capital extension installed</p>
        <p>2. Navigate to a LinkedIn profile page (e.g., <a href="https://www.linkedin.com/in/luke-t-7963078a/" target="_blank">Luke's Profile</a>)</p>
        <p>3. Look for the "Outreach Template" button in the Blue Wire Capital admin panel</p>
        <p>4. Click the button to test the outreach template functionality</p>
        <p>5. The outreach message should be copied to your clipboard</p>
    </div>

    <div class="test-section">
        <h2>Extension Status</h2>
        <button onclick="checkExtension()">Check Extension Status</button>
        <div id="extension-status"></div>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <button onclick="testClipboard()">Test Clipboard Access</button>
        <button onclick="clearResults()">Clear Results</button>
        <div id="test-results"></div>
    </div>

    <script>
        function checkExtension() {
            const statusDiv = document.getElementById('extension-status');
            
            // Check if we're on a LinkedIn page
            const isLinkedIn = window.location.hostname.includes('linkedin.com');
            
            if (isLinkedIn) {
                statusDiv.innerHTML = '<div class="success">✅ You are on LinkedIn - extension should be active</div>';
            } else {
                statusDiv.innerHTML = '<div class="error">❌ You are not on LinkedIn - navigate to a LinkedIn profile to test</div>';
            }
        }

        async function testClipboard() {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                // Test clipboard write
                const testData = 'Test clipboard write - ' + new Date().toISOString();
                await navigator.clipboard.writeText(testData);
                
                // Test clipboard read
                const clipboardContent = await navigator.clipboard.readText();
                
                if (clipboardContent === testData) {
                    resultsDiv.innerHTML += '<div class="success">✅ Clipboard access working correctly</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="error">❌ Clipboard read/write mismatch</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML += '<div class="error">❌ Clipboard access failed: ' + error.message + '</div>';
            }
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('extension-status').innerHTML = '';
        }

        // Auto-check extension status on load
        window.addEventListener('load', checkExtension);
    </script>
</body>
</html>
